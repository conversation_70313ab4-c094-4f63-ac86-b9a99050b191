i was stupid enough to leave "8321" USDC in TRUNK-USDC pool on "Save.Finance" protocol (previusly named "Solend" protocol) on Solana blockchain and now TRUNK token is almost worthless and i cannot withdraw my staked USDC. I didn't deposit (or borrow) any TRUNK tokens and it doesn't show that i have any. My USDC is stuck in a pool due to 100% utilization - all USDC has been borrowed, so withdrawal is impossible until borrowers repay. 

can you help me writting a script that tracks balance change in this pool? i'd prefer you to use typescript and pure node js if possible.
every 30 seconds it should update my wallet balance (SOL and USDC), total USDC and TRUNK locked in the pool, my currently deposited USDC and TRUNK tokens, USDC that can be withdrawn. i want to be first who knows that USDC can be withdrawn and withdraw it.

The pool operates on Solana blockchain. It is permissionless, NOT a standard Solend main market pool.
Contains two reserves:
TRUNK: 9mV4WUukVsva5wYcYW4veo34CNDiF44sh3Ji65JNdvh5 (reserve: 9SdGVn594S8fk8ZU3xcvZ1Cd4fjJgNxCN32upHhFZgJu)
USDC: EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v (reserve: 8nXn5zEXFbAvXyeYgLLethMVfHxU4QgyG2GVrHmKojqR)

I have the pool's web page (https://save.finance/?pool=616Kxm68sCsFJTzKfqJYt1o2Na5qrujgfhrhUJsSPXHx&obligation=BpgFy4iymzUjLzWiP3fGVASnLh1wqcT7NWnoTcybMQwg) saved as "TRUNK_USDC.mhtml" file.

I recorded the network traffic on this page after i load it, connect my wallet and refresh my pool balance. the traffic info is stored in "save.finance.har" file.

feel free to fetch all the necessary information from these files page. 
if you need something from me, then give me very clear instruction of what to do, cause i'm pretty dumm with web tools. 
you can use any tools you need and ask me to do anything for you to get this done, but keep in mind that i'm dumm.